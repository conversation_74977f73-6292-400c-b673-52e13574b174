{"id": "s3-sync-memory-safe-batch-daily", "type": "batch", "description": "Daily memory-safe download of all photos split by device", "schedule": {"cron": "0 2 * * *", "max_instances": 1, "overlap_policy": "skip"}, "tasks": [{"id": "amanda-photos", "type": "tool", "description": "Download <PERSON>'s iPhone photos", "tool": "s3_sync", "args": {"direction": "down", "bucket": "<PERSON><PERSON><PERSON>-photos", "prefix": "amandas iPhone/", "local_path": "E:/S3/kester<PERSON>-photos/amandas iPhone/", "dry_run": false, "include": ["*.jpg", "*.png", "*.mov", "*.mp4"], "exclude": ["*.tmp", "*.log"]}}, {"id": "dylan-photos", "type": "tool", "description": "Download <PERSON>'s iPhone photos", "tool": "s3_sync", "args": {"direction": "down", "bucket": "<PERSON><PERSON><PERSON>-photos", "prefix": "<PERSON><PERSON><PERSON>'s iPhone/", "local_path": "E:/S3/kester<PERSON>-photos/d<PERSON><PERSON>'s iPhone/", "dry_run": false, "include": ["*.jpg", "*.png", "*.mov", "*.mp4"], "exclude": ["*.tmp", "*.log"]}}], "metadata": {"priority": "normal", "tags": ["s3", "backup", "photos", "batch", "scheduled", "daily"], "created_by": "user", "notes": "Daily scheduled backup at 2:00 AM. Memory-safe batch download split by device to prevent memory issues. Downloads to E: drive."}}