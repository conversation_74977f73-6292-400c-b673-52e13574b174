flowchart LR
  CLI_Tools["CLI Tools & Scripts"]
  Orchestrator["Task Orchestrator"]
  MediaProc["Media Processors"]
  AI_Services["AI Services"]
  SearchEngines["Search Engines"]
  Database[("Database")]
  Analytics["AnalyticsLogger"]
  Scheduler["Task Scheduler & Rules Engine"]
  LearningSvc["EnhancedLearningService"]
  FeedbackSvc["User Feedback & Patterns"]
  Monitoring["Live Monitoring & Dashboard"]
  MCP_Servers["MCP Servers"]
  RetryManager["Retry Manager"]

  CLI_Tools --> Orchestrator
  Orchestrator --> MediaProc
  MediaProc --> AI_Services
  AI_Services --> SearchEngines
  Orchestrator --> Database
  Orchestrator --> Analytics
  Analytics --> Database
  Orchestrator --> Scheduler
  Scheduler --> Database
  Orchestrator --> LearningSvc
  LearningSvc --> Database
  Orchestrator --> FeedbackSvc
  FeedbackSvc --> Database
  Orchestrator --> Monitoring
  Monitoring --> Database
  Orchestrator --> RetryManager
  RetryManager --> Database
  Orchestrator --> MCP_Servers
  MCP_Servers --> Orchestrator
  SearchEngines --> Database
