<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Task Monitor Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .status-pending { @apply bg-yellow-100 text-yellow-800; }
        .status-running { @apply bg-blue-100 text-blue-800; }
        .status-completed { @apply bg-green-100 text-green-800; }
        .status-failed { @apply bg-red-100 text-red-800; }
        .status-blocked { @apply bg-gray-100 text-gray-800; }
        .status-skipped { @apply bg-gray-100 text-gray-800; }
        .status-retrying { @apply bg-orange-100 text-orange-800; }
        
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .connection-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .connected { background-color: #10b981; }
        .disconnected { background-color: #ef4444; }
        .connecting { background-color: #f59e0b; }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto py-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <div class="flex justify-between items-center">
                <h1 class="text-2xl font-bold">Live Task Monitor Dashboard</h1>
                <div class="flex items-center">
                    <span class="connection-indicator" id="connectionIndicator"></span>
                    <span id="connectionStatus">Connecting...</span>
                    <button id="reconnectBtn" class="ml-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 hidden">
                        Reconnect
                    </button>
                </div>
            </div>
            <div class="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600" id="totalTasks">-</div>
                    <div class="text-sm text-gray-600">Total Tasks</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600" id="completedTasks">-</div>
                    <div class="text-sm text-gray-600">Completed</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-yellow-600" id="runningTasks">-</div>
                    <div class="text-sm text-gray-600">Running</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-red-600" id="failedTasks">-</div>
                    <div class="text-sm text-gray-600">Failed</div>
                </div>
            </div>
        </div>

        <!-- Real-time Updates -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-lg font-semibold mb-4">Real-time Updates</h2>
            <div id="realtimeUpdates" class="h-32 overflow-y-auto bg-gray-50 p-4 rounded">
                <div class="text-gray-500 text-sm">Waiting for updates...</div>
            </div>
        </div>

        <!-- Task List -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold mb-4">Recent Tasks</h2>
            <div class="overflow-x-auto">
                <table class="min-w-full">
                    <thead>
                        <tr class="border-b">
                            <th class="px-4 py-2 text-left">ID</th>
                            <th class="px-4 py-2 text-left">Type</th>
                            <th class="px-4 py-2 text-left">Description</th>
                            <th class="px-4 py-2 text-left">Status</th>
                            <th class="px-4 py-2 text-left">Created</th>
                            <th class="px-4 py-2 text-left">Duration</th>
                        </tr>
                    </thead>
                    <tbody id="taskTableBody">
                        <tr>
                            <td colspan="6" class="px-4 py-8 text-center text-gray-500">
                                Loading tasks...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        class LiveDashboard {
            constructor() {
                this.ws = null;
                this.reconnectAttempts = 0;
                this.maxReconnectAttempts = 5;
                this.reconnectDelay = 1000;
                this.tasks = [];
                
                this.initializeElements();
                this.connect();
                this.setupEventListeners();
            }
            
            initializeElements() {
                this.connectionIndicator = document.getElementById('connectionIndicator');
                this.connectionStatus = document.getElementById('connectionStatus');
                this.reconnectBtn = document.getElementById('reconnectBtn');
                this.realtimeUpdates = document.getElementById('realtimeUpdates');
                this.taskTableBody = document.getElementById('taskTableBody');
                
                // Stats elements
                this.totalTasks = document.getElementById('totalTasks');
                this.completedTasks = document.getElementById('completedTasks');
                this.runningTasks = document.getElementById('runningTasks');
                this.failedTasks = document.getElementById('failedTasks');
            }
            
            setupEventListeners() {
                this.reconnectBtn.addEventListener('click', () => {
                    this.connect();
                });
            }
            
            connect() {
                this.updateConnectionStatus('connecting', 'Connecting...');
                this.reconnectBtn.classList.add('hidden');
                
                try {
                    this.ws = new WebSocket('ws://localhost:8080');
                    
                    this.ws.onopen = () => {
                        this.updateConnectionStatus('connected', 'Connected');
                        this.reconnectAttempts = 0;
                        this.addRealtimeUpdate('Connected to live monitoring', 'success');
                    };
                    
                    this.ws.onmessage = (event) => {
                        try {
                            const message = JSON.parse(event.data);
                            this.handleMessage(message);
                        } catch (error) {
                            console.error('Error parsing WebSocket message:', error);
                        }
                    };
                    
                    this.ws.onclose = () => {
                        this.updateConnectionStatus('disconnected', 'Disconnected');
                        this.addRealtimeUpdate('Connection lost', 'error');
                        this.scheduleReconnect();
                    };
                    
                    this.ws.onerror = (error) => {
                        console.error('WebSocket error:', error);
                        this.addRealtimeUpdate('Connection error', 'error');
                    };
                    
                } catch (error) {
                    console.error('Failed to create WebSocket connection:', error);
                    this.updateConnectionStatus('disconnected', 'Connection failed');
                    this.scheduleReconnect();
                }
            }
            
            scheduleReconnect() {
                if (this.reconnectAttempts < this.maxReconnectAttempts) {
                    this.reconnectAttempts++;
                    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
                    
                    setTimeout(() => {
                        this.addRealtimeUpdate(`Reconnecting... (attempt ${this.reconnectAttempts})`, 'info');
                        this.connect();
                    }, delay);
                } else {
                    this.reconnectBtn.classList.remove('hidden');
                    this.addRealtimeUpdate('Max reconnection attempts reached. Click reconnect to try again.', 'error');
                }
            }
            
            updateConnectionStatus(status, text) {
                this.connectionIndicator.className = `connection-indicator ${status}`;
                this.connectionStatus.textContent = text;
                
                if (status === 'connecting') {
                    this.connectionIndicator.classList.add('pulse-animation');
                } else {
                    this.connectionIndicator.classList.remove('pulse-animation');
                }
            }
            
            handleMessage(message) {
                switch (message.type) {
                    case 'current_status':
                        this.updateTaskList(message.data.tasks);
                        this.addRealtimeUpdate('Received current status', 'info');
                        break;
                        
                    case 'status_update':
                        this.handleStatusUpdate(message.data);
                        break;
                        
                    default:
                        console.log('Unknown message type:', message.type);
                }
            }
            
            handleStatusUpdate(statusUpdate) {
                const { taskId, status, timestamp, details } = statusUpdate;
                
                // Update task in list if it exists
                const taskIndex = this.tasks.findIndex(task => task.id == taskId);
                if (taskIndex !== -1) {
                    this.tasks[taskIndex].status = status;
                    this.updateTaskList(this.tasks);
                }
                
                // Add to real-time updates
                this.addRealtimeUpdate(
                    `Task ${taskId} → ${status}`,
                    this.getStatusType(status),
                    timestamp
                );
            }
            
            updateTaskList(tasks) {
                this.tasks = tasks || [];
                this.updateStats();
                this.renderTaskTable();
            }
            
            updateStats() {
                const stats = this.tasks.reduce((acc, task) => {
                    acc.total++;
                    acc[task.status] = (acc[task.status] || 0) + 1;
                    return acc;
                }, { total: 0 });
                
                this.totalTasks.textContent = stats.total;
                this.completedTasks.textContent = stats.completed || 0;
                this.runningTasks.textContent = stats.running || 0;
                this.failedTasks.textContent = (stats.failed || 0) + (stats.error || 0);
            }
            
            renderTaskTable() {
                if (this.tasks.length === 0) {
                    this.taskTableBody.innerHTML = `
                        <tr>
                            <td colspan="6" class="px-4 py-8 text-center text-gray-500">
                                No tasks found
                            </td>
                        </tr>
                    `;
                    return;
                }
                
                this.taskTableBody.innerHTML = this.tasks.slice(0, 20).map(task => {
                    const duration = this.calculateDuration(task);
                    return `
                        <tr class="border-b hover:bg-gray-50">
                            <td class="px-4 py-2">${task.id}</td>
                            <td class="px-4 py-2">${task.type}</td>
                            <td class="px-4 py-2 max-w-xs truncate" title="${task.description}">
                                ${task.description || 'No description'}
                            </td>
                            <td class="px-4 py-2">
                                <span class="px-2 py-1 rounded text-xs status-${task.status}">
                                    ${task.status}
                                </span>
                            </td>
                            <td class="px-4 py-2 text-sm">
                                ${this.formatTimestamp(task.created_at)}
                            </td>
                            <td class="px-4 py-2 text-sm">
                                ${duration}
                            </td>
                        </tr>
                    `;
                }).join('');
            }
            
            calculateDuration(task) {
                if (!task.started_at) return '-';
                
                const start = new Date(task.started_at);
                const end = task.finished_at ? new Date(task.finished_at) : new Date();
                const duration = Math.floor((end - start) / 1000);
                
                if (duration < 60) return `${duration}s`;
                if (duration < 3600) return `${Math.floor(duration / 60)}m ${duration % 60}s`;
                return `${Math.floor(duration / 3600)}h ${Math.floor((duration % 3600) / 60)}m`;
            }
            
            formatTimestamp(timestamp) {
                if (!timestamp) return '-';
                return new Date(timestamp).toLocaleString();
            }
            
            addRealtimeUpdate(message, type = 'info', timestamp = null) {
                const time = timestamp ? new Date(timestamp) : new Date();
                const timeStr = time.toLocaleTimeString();
                
                const typeClasses = {
                    success: 'text-green-600',
                    error: 'text-red-600',
                    info: 'text-blue-600',
                    warning: 'text-yellow-600'
                };
                
                const updateElement = document.createElement('div');
                updateElement.className = `text-sm mb-1 ${typeClasses[type] || 'text-gray-600'}`;
                updateElement.innerHTML = `<span class="text-gray-500">[${timeStr}]</span> ${message}`;
                
                this.realtimeUpdates.insertBefore(updateElement, this.realtimeUpdates.firstChild);
                
                // Keep only last 50 updates
                while (this.realtimeUpdates.children.length > 50) {
                    this.realtimeUpdates.removeChild(this.realtimeUpdates.lastChild);
                }
            }
            
            getStatusType(status) {
                const statusTypes = {
                    completed: 'success',
                    failed: 'error',
                    error: 'error',
                    running: 'info',
                    pending: 'warning'
                };
                return statusTypes[status] || 'info';
            }
        }
        
        // Initialize dashboard when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new LiveDashboard();
        });
    </script>
</body>
</html>
